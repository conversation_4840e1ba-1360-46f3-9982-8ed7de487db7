{% load static %}
{% load i18n %}
{% load image_utils %}
<!DOCTYPE html>
<html lang="{{ current_language }}" dir="{{ text_direction }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="HR Management System - Comprehensive employee management solution">
    <title>{% block title %}نظام إدارة الموارد البشرية{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">

    <!-- Bootstrap RTL/LTR -->
    {% if text_direction == 'rtl' %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Google Fonts -->
    {% if system_settings.font_family == 'cairo' or current_font == 'Cairo' %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'tajawal' or current_font == 'Tajawal' %}
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'almarai' or current_font == 'Almarai' %}
    <link href="https://fonts.googleapis.com/css2?family=Almarai:wght@400;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'ibm-plex-sans-arabic' or current_font == 'IBM Plex Sans Arabic' %}
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@400;500;600&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'noto-sans-arabic' or current_font == 'Noto Sans Arabic' %}
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    {% else %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    {% endif %}

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style_updated.css' %}">
    <link rel="stylesheet" href="{% static 'css/responsive.css' %}">
    <link rel="stylesheet" href="{% static 'css/animations.css' %}">

    <style>
        :root {
            --font-family: {% with default_font='Cairo' %}{{ system_settings.font_family|default:current_font|default:default_font }}{% endwith %}, sans-serif;
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --success-color: #2ecc71;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #3498db;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --body-bg: #f8f9fa;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
            --transition-speed: 0.3s;
            --header-height: 60px;
            --sidebar-width: 250px;
            --sidebar-collapsed-width: 70px;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--body-bg);
            transition: background-color var(--transition-speed);
        }

        .navbar {
            background-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            height: var(--header-height);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
        }

        .navbar-brand {
            font-weight: bold;
            color: white !important;
            transition: opacity var(--transition-speed);
        }



        .navbar-brand:hover {
            opacity: 0.9;
        }





        .with-sidebar {


            padding: 0;
        }

        .page-wrapper {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            padding-top: var(--header-height);
        }

        .content-wrapper {
            display: flex;
            flex: 1;
        }

        .sidebar {

            width: var(--sidebar-width);
            background-color: var(--secondary-color);
            color: white;
            padding-top: 20px;

            transition: width var(--transition-speed), transform var(--transition-speed);
            position: fixed;

            height: calc(100vh - var(--header-height));
            top: var(--header-height);
            z-index: 100;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
        }





        .sidebar::-webkit-scrollbar {
            width: 6px;
        }





        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }





        .sidebar::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
        }































        .main-content {
            flex: 1;
            padding: 20px;


            margin-right: var(--sidebar-width);
            transition: margin var(--transition-speed);
        }















        @media (max-width: 768px) {
            .sidebar {


                transform: translateX(100%);


            }

            .main-content {
                margin-right: 0;
            }

            .sidebar.show {


                transform: translateX(0);

            }
















        }




        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);


            transition: transform 0.3s, box-shadow 0.3s;
        }





















        .card:hover {
            transform: translateY(-5px);

            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }









        .stats-card {


            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-left: 4px solid var(--primary-color);
        }
















































        .stats-number {
            font-size: 2rem;
            font-weight: 700;



            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            background-clip: text;


            -webkit-text-fill-color: transparent;

        }































































































































































        .form-control, .form-select {





            border-radius: var(--border-radius);
            border: 1px solid rgba(0, 0, 0, 0.1);
            padding: 0.5rem 1rem;
            transition: all var(--transition-speed);
        }















        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }





        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }









        .animate-fade-in-up {
            animation: fadeInUp 0.5s ease-out;
        }















    </style>
    {% block extra_css %}{% endblock %}
</head>
</head>
</head>
<body>
    <div class="page-wrapper">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light with-sidebar">
            <div class="container-fluid">
                <div class="d-flex align-items-center">
                    <button id="sidebarToggle" class="btn btn-light me-2">
                        <i class="fas fa-bars"></i>
                    </button>
                    <a class="navbar-brand" href="{% url 'Hr:dashboard' %}">
                        <i class="fas fa-users me-2"></i>
                        <span>نظام الموارد البشرية</span>
                    </a>
                </div>
                <div class="d-flex align-items-center">
                    <div class="user-info me-3 d-flex align-items-center">
                        <i class="fas fa-user-circle text-light me-2 fs-5"></i>
                        <span class="text-light fw-bold">{{ request.user.username }}</span>
                    </div>
                    <a href="{% url 'accounts:logout' %}" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i> تسجيل الخروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="content-wrapper">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h3><i class="fas fa-users me-2"></i> الموارد البشرية</h3>
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                        <a href="{% url 'Hr:dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'list' and 'employees' in request.path %}active{% endif %}">
                        <a href="{% url 'Hr:employees:list' %}">
                            <i class="fas fa-users"></i>
                            <span>قائمة الموظفين</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'employee_detail_view' %}active{% endif %}">
                        <a href="{% url 'Hr:detail_view' %}">
                            <i class="fas fa-id-card"></i>
                            <span>بيانات الموظف تفصيلي</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'list' and 'departments' in request.path %}active{% endif %}">
                        <a href="{% url 'Hr:departments:list' %}">
                            <i class="fas fa-building"></i>
                            <span>الأقسام</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'list' and 'jobs' in request.path %}active{% endif %}">
                        <a href="{% url 'Hr:jobs:list' %}">
                            <i class="fas fa-briefcase"></i>
                            <span>الوظائف</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'salary_item_list' %}active{% endif %}">
                        <a href="{% url 'Hr:salary_item_list' %}">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>الرواتب</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if 'attendance/dashboard' in request.path %}active{% endif %}">
                        <a href="{% url 'attendance:dashboard' %}">
                            <i class="fas fa-clock"></i>
                            <span>نظام الحضور والانصراف</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'record_list' %}active{% endif %}">
                        <a href="{% url 'attendance:record_list' %}">
                            <i class="fas fa-clipboard-check"></i>
                            <span>سجل الحضور والانصراف</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'mark_attendance' %}active{% endif %}">
                        <a href="{% url 'attendance:mark_attendance' %}">
                            <i class="fas fa-fingerprint"></i>
                            <span>تسجيل الحضور</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'list' and 'alerts' in request.path %}active{% endif %}">
                        <a href="{% url 'Hr:alerts:list' %}">
                            <i class="fas fa-bell"></i>
                            <span>التنبيهات</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'accounts:home' %}">
                            <i class="fas fa-home"></i>
                            <span>الصفحة الرئيسية</span>
                        </a>
                    </li>

                    <!-- Navigation menu items -->
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'attendance_rule_list' %}active{% endif %}">
                        <a href="{% url 'Hr:attendance:attendance_rule_list' %}">
                            <i class="fas fa-clock"></i>
                            <span>قواعد الحضور</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'attendance_record_list' %}active{% endif %}">
                        <a href="{% url 'Hr:attendance:attendance_record_list' %}">
                            <i class="fas fa-clipboard-check"></i>
                            <span>سجلات الحضور</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'employee_attendance_rule_list' %}active{% endif %}">
                        <a href="{% url 'Hr:attendance:employee_attendance_rule_list' %}">
                            <i class="fas fa-user-clock"></i>
                            <span>قواعد حضور الموظفين</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'official_holiday_list' %}active{% endif %}">
                        <a href="{% url 'Hr:attendance:official_holiday_list' %}">
                            <i class="fas fa-calendar"></i>
                            <span>الإجازات الرسمية</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'update_data' %}active{% endif %}">
                        <a href="{% url 'Hr:update_data' %}">
                            <i class="fas fa-sync-alt"></i>
                            <span>تحديث البيانات</span>
                        </a>
                    </li>
                </ul>
            </aside>

            <!-- Main Content -->
            <div class="main-content">
                {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Page Header -->
                <div class="page-header mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1">{% block page_title %}الموارد البشرية{% endblock %}</h2>
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb mb-0">
                                    {% block breadcrumb %}
                                    <li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
                                    <li class="breadcrumb-item active">الموارد البشرية</li>
                                    {% endblock %}
                                </ol>
                            </nav>
                        </div>
                        <div class="page-actions">
                            {% block page_actions %}{% endblock %}
                        </div>
                    </div>
                </div>

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar Toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            const pageWrapper = document.querySelector('.page-wrapper');
            const sidebar = document.querySelector('.sidebar');

            // Create overlay element for mobile
            const overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            document.body.appendChild(overlay);

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    pageWrapper.classList.toggle('sidebar-collapsed');

                    // For mobile devices
                    if (window.innerWidth <= 768) {
                        sidebar.classList.toggle('show');
                        overlay.classList.toggle('show');
                    }
                });
            }

            // Close sidebar when clicking on overlay
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
                pageWrapper.classList.remove('sidebar-collapsed');
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    overlay.classList.remove('show');
                }
            });

            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);

            // Highlight active menu item
            const currentPath = window.location.pathname;
            const menuItems = document.querySelectorAll('.sidebar-menu-item');

            menuItems.forEach(item => {
                const link = item.querySelector('a');
                if (link && currentPath.includes(link.getAttribute('href'))) {
                    item.classList.add('active');
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
