{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load image_utils %}
{% load form_utils %}
{% load django_permissions %}

{% block title %}قائمة الموظفين - نظام الدولية{% endblock %}

{% block page_title %}قائمة الموظفين{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item active">قائمة الموظفين</li>
{% endblock %}

{% block content %}
<div class="row mb-4 g-3">
    <div class="col-xl-3 col-md-6">
        <div class="card stats-card bg-gradient-primary border-0 h-100 hover-shadow">
            <div class="card-body d-flex align-items-center p-4">
                <div class="stats-icon-circle bg-white bg-opacity-25 me-3">
                    <i class="fas fa-users fa-lg text-white"></i>
                </div>
                <div>
                    <h3 class="stats-number text-white mb-1">{{ total_employees }}</h3>
                    <p class="stats-title text-white text-opacity-75 mb-0">إجمالي الموظفين</p>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0 py-3">
                <div class="progress bg-white bg-opacity-25" style="height: 4px;">
                    <div class="progress-bar bg-white" style="width: 100%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card stats-card bg-gradient-success border-0 h-100 hover-shadow">
            <div class="card-body d-flex align-items-center p-4">
                <div class="stats-icon-circle bg-white bg-opacity-25 me-3">
                    <i class="fas fa-user-check fa-lg text-white"></i>
                </div>
                <div>
                    <h3 class="stats-number text-white mb-1">{{ active_employees }}</h3>
                    <p class="stats-title text-white text-opacity-75 mb-0">المؤمن عليهم</p>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0 py-3">
                <div class="progress bg-white bg-opacity-25" style="height: 4px;">
                    <div class="progress-bar bg-white" style="width: {{ active_employees|divisibleby:total_employees|floatformat:2 }}%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card stats-card bg-gradient-info border-0 h-100 hover-shadow">
            <div class="card-body d-flex align-items-center p-4">
                <div class="stats-icon-circle bg-white bg-opacity-25 me-3">
                    <i class="fas fa-pause-circle fa-lg text-white"></i>
                </div>
                <div>
                    <h3 class="stats-number text-white mb-1">{{ on_leave_employees }}</h3>
                    <p class="stats-title text-white text-opacity-75 mb-0">الإجازات</p>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0 py-3">
                <div class="progress bg-white bg-opacity-25" style="height: 4px;">
                    <div class="progress-bar bg-white" style="width: {% widthratio on_leave_employees total_employees 100 %}%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card stats-card bg-gradient-danger border-0 h-100 hover-shadow">
            <div class="card-body d-flex align-items-center p-4">
                <div class="stats-icon-circle bg-white bg-opacity-25 me-3">
                    <i class="fas fa-user-times fa-lg text-white"></i>
                </div>
                <div>
                    <h3 class="stats-number text-white mb-1">{{ resigned_employees }}</h3>
                    <p class="stats-title text-white text-opacity-75 mb-0">المستقيلين</p>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0 py-3">
                <div class="progress bg-white bg-opacity-25" style="height: 4px;">
                    <div class="progress-bar bg-white" style="width: {% widthratio resigned_employees total_employees 100 %}%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="d-flex align-items-center mb-4 p-3 bg-light rounded shadow-sm">
    <div class="toggle-border me-3">
        <input id="employeeStatusToggle" type="checkbox" {% if status != 'inactive' %}checked{% endif %}>
        <label for="employeeStatusToggle">
            <div class="handle"></div>
        </label>
    </div>
    <span id="toggleStatusText" class="fw-bold {% if status == 'inactive' %}text-danger{% else %}text-success{% endif %}">
        {% if status == 'inactive' %}موظفين غير نشطين{% else %}موظفين نشطين{% endif %}
    </span>
</div>


<div class="row g-4">
    <div class="col-12 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-light py-3 border-bottom">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-filter me-2 text-primary"></i>
                    تصفية النتائج
                </h5>
            </div>
            <div class="card-body p-4">
                <form method="get" action="" id="employeeFilterForm">
                    <!-- إذا كانت هناك فلاتر نشطة، نعرض ملخص لها -->
                    {% if request.GET %}
                    <div class="active-filters mb-3 p-2 bg-light rounded border">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="small fw-medium text-primary">
                                <i class="fas fa-filter me-1"></i> الفلاتر النشطة
                            </span>
                            <a href="{% url 'Hr:employees:list' %}" class="btn btn-sm btn-outline-secondary px-2 py-0">
                                <i class="fas fa-times-circle"></i> مسح الكل
                            </a>
                        </div>
                        <div class="active-filter-tags d-flex flex-wrap gap-1">
                            {% if request.GET.search %}
                            <span class="badge bg-primary-subtle text-primary">
                                الاسم: {{ request.GET.search }}
                                <a href="#" class="text-primary ms-1 remove-filter" data-field="search"><i class="fas fa-times-circle"></i></a>
                            </span>
                            {% endif %}
                            {% if request.GET.emp_code %}
                            <span class="badge bg-primary-subtle text-primary">
                                الكود: {{ request.GET.emp_code }}
                                <a href="#" class="text-primary ms-1 remove-filter" data-field="emp_code"><i class="fas fa-times-circle"></i></a>
                            </span>
                            {% endif %}
                            {% if request.GET.department %}
                            <span class="badge bg-primary-subtle text-primary">
                                القسم: {{ filter_form.department.field.choices|dictsort:"0"|dict_get:request.GET.department }}
                                <a href="#" class="text-primary ms-1 remove-filter" data-field="department"><i class="fas fa-times-circle"></i></a>
                            </span>
                            {% endif %}
                            {% if request.GET.job %}
                            <span class="badge bg-primary-subtle text-primary">
                                الوظيفة
                                <a href="#" class="text-primary ms-1 remove-filter" data-field="job"><i class="fas fa-times-circle"></i></a>
                            </span>
                            {% endif %}
                            {% if request.GET.working_condition and request.GET.working_condition != 'سارى' %}
                            <span class="badge bg-primary-subtle text-primary">
                                حالة العمل: {{ request.GET.working_condition }}
                                <a href="#" class="text-primary ms-1 remove-filter" data-field="working_condition"><i class="fas fa-times-circle"></i></a>
                            </span>
                            {% endif %}
                            {% if request.GET.insurance_status %}
                            <span class="badge bg-primary-subtle text-primary">
                                حالة التأمين: {{ request.GET.insurance_status }}
                                <a href="#" class="text-primary ms-1 remove-filter" data-field="insurance_status"><i class="fas fa-times-circle"></i></a>
                            </span>
                            {% endif %}
                            <!-- عرض الفلاتر المتقدمة النشطة -->
                            {% comment %} Advanced filter tags were here {% endcomment %}
                        </div>
                    </div>
                    {% endif %}

                    <div class="search-section mb-4">
                        <div class="input-group mb-3 search-main">
                            <span class="input-group-text bg-light"><i class="fas fa-search text-primary"></i></span>
                            <input type="text" name="search" class="form-control search-autocomplete" placeholder="بحث سريع بالاسم أو الكود أو الرقم القومي" value="{{ request.GET.search|default:'' }}" autocomplete="off">
                            <button type="submit" class="btn btn-primary">بحث</button>
                        </div>
                        <div class="search-results-container position-relative">
                            <div class="search-results-dropdown d-none position-absolute w-100 bg-white shadow-sm rounded border z-3" style="max-height: 300px; overflow-y: auto;"></div>
                        </div>
                    </div>

                    <div class="filter-groups">
                        <!-- مجموعة الفلاتر الأساسية -->
                        <div class="filter-group mb-3">
                            <h6 class="filter-group-title mb-2 fw-medium text-dark">
                                <i class="fas fa-filter me-1 text-primary"></i> الفلاتر الأساسية
                            </h6>

                            <div class="row g-2">
                                <div class="col-md-6 mb-2">
                                    <label class="form-label small text-muted">
                                        <i class="fas fa-id-badge me-1 text-primary"></i>
                                        بحث بالكود
                                    </label>
                                    {% if filter_form.search_empID %}
                                        {{ filter_form.search_empID }}
                                    {% else %}
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text bg-light"><i class="fas fa-search text-muted small"></i></span>
                                            <input type="text" name="emp_code" class="form-control form-control-sm" placeholder="أدخل كود الموظف" value="{{ request.GET.emp_code|default:'' }}">
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-6 mb-2">
                                    <label class="form-label small text-muted">{{ filter_form.department.label }}</label>
                                    {{ filter_form.department }}
                                </div>

                                <div class="col-md-6 mb-2">
                                    <label class="form-label small text-muted">{{ filter_form.job.label }}</label>
                                    {{ filter_form.job }}
                                </div>

                                <div class="col-md-6 mb-2">
                                    <label class="form-label small text-muted">{{ filter_form.working_condition.label }}</label>
                                    {{ filter_form.working_condition }}
                                </div>

                                <div class="col-md-6 mb-2">
                                    <label class="form-label small text-muted">{{ filter_form.insurance_status.label }}</label>
                                    {{ filter_form.insurance_status }}
                                </div>
                            </div>
                        </div>
                    </div>

                    {% comment %} Advanced search section removed {% endcomment %}

                    <div class="d-flex flex-wrap gap-2 mt-4">
                        <button type="submit" class="btn btn-primary flex-grow-1">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                        <a href="{% url 'Hr:employees:list' %}" class="btn btn-outline-secondary flex-grow-1">
                            <i class="fas fa-sync-alt me-1"></i>
                            إعادة تعيين
                        </a>
                        {% comment %} Toggle advanced search button removed {% endcomment %}
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center py-3 border-bottom">
                <div>
                    <h5 id="employeeListTitle" class="mb-0 fw-semibold">
                        {% if status == 'inactive' %}
                        قائمة الموظفين غير النشطين
                        {% else %}
                        قائمة الموظفين النشطين
                        {% endif %}
                    </h5>
                    {% if employees %}
                    <small class="text-muted mt-1 d-block">
                        <i class="fas fa-users me-1"></i> تم العثور على <span class="fw-bold text-primary">{{ employees|length }}</span> موظف
                        {% if request.GET %}
                        <span class="text-muted">مطابق لمعايير البحث</span>
                        {% endif %}
                    </small>
                    {% endif %}
                </div>
                <div class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-sort me-1"></i>
                            ترتيب
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                            <li><a class="dropdown-item sort-option" href="#" data-sort="emp_id"><i class="fas fa-sort-numeric-down me-2"></i>رقم الموظف</a></li>
                            <li><a class="dropdown-item sort-option" href="#" data-sort="emp_full_name"><i class="fas fa-sort-alpha-down me-2"></i>الاسم</a></li>
                            <li><a class="dropdown-item sort-option" href="#" data-sort="department"><i class="fas fa-sort-alpha-down me-2"></i>القسم</a></li>
                            <li><a class="dropdown-item sort-option" href="#" data-sort="emp_date_hiring"><i class="fas fa-sort-numeric-down me-2"></i>تاريخ التعيين</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item sort-direction" href="#" data-direction="asc"><i class="fas fa-arrow-up me-2"></i>تصاعدي</a></li>
                            <li><a class="dropdown-item sort-direction" href="#" data-direction="desc"><i class="fas fa-arrow-down me-2"></i>تنازلي</a></li>
                        </ul>
                    </div>
                    <div class="actions text-end mb-3">
                        {% if perms.Hr.add_employee or user|is_admin %}
                            <a href="{% url 'Hr:employees:create' %}" class="btn btn-primary">
                                <i class="fas fa-user-plus me-1"></i> إضافة موظف جديد
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                {% if employees %}
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0" id="employeesTable">
                        <thead class="table-light sticky-top">
                            <tr>
                                <th class="py-3 px-3 sortable" data-sort="emp_id">
                                    <div class="d-flex align-items-center">
                                        الرقم
                                        <i class="fas fa-sort ms-1 text-muted small"></i>
                                    </div>
                                </th>
                                <th class="py-3 px-3 sortable" data-sort="emp_full_name">
                                    <div class="d-flex align-items-center">
                                        الاسم
                                        <i class="fas fa-sort ms-1 text-muted small"></i>
                                    </div>
                                </th>
                                <th class="py-3 px-3 sortable" data-sort="department">
                                    <div class="d-flex align-items-center">
                                        القسم
                                        <i class="fas fa-sort ms-1 text-muted small"></i>
                                    </div>
                                </th>
                                <th class="py-3 px-3">الوظيفة</th>
                                <th class="py-3 px-3">الهاتف</th>
                                <th class="py-3 px-3 sortable" data-sort="working_condition">
                                    <div class="d-flex align-items-center">
                                        الحالة
                                        <i class="fas fa-sort ms-1 text-muted small"></i>
                                    </div>
                                </th>
                                <th class="py-3 px-3 text-center">العمليات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees %}
                            <tr class="employee-row" data-emp-id="{{ employee.emp_id }}" data-emp-name="{{ employee.emp_full_name|default:employee.emp_first_name }}" data-dept="{{ employee.department.dept_name|default:'' }}" data-condition="{{ employee.working_condition|default:'' }}">
                                <td class="px-3">
                                    <span class="badge bg-light text-dark border">{{ employee.emp_id }}</span>
                                </td>
                                <td class="px-3">
                                    <div class="d-flex align-items-center py-2">
                                        {% if employee.emp_image %}
                                        <img src="{{ employee.emp_image|binary_to_img }}" alt="{{ employee.emp_full_name }}" class="rounded-circle me-3 object-fit-cover employee-table-img" width="50" height="50">
                                        {% else %}
                                        <div class="avatar bg-primary text-white me-3 flex-shrink-0" style="width: 50px; height: 50px;">
                                            {{ employee.emp_first_name|slice:":1"|upper }}
                                        </div>
                                        {% endif %}
                                        <div>
                                            <a href="{% url 'Hr:employees:detail' employee.emp_id %}" class="fw-medium d-block text-decoration-none text-dark employee-name">{{ employee.emp_full_name|default:employee.emp_first_name }}</a>
                                            <small class="text-muted">{{ employee.national_id|default:'' }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-3">
                                    {% if employee.department %}
                                    <span class="department-name">{{ employee.department.dept_name }}</span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                  <td class="px-3">
                                    {% if employee.job %}
                                    <span class="job-name">{{ employee.job_name }}</span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td class="px-3">
                                    {% if employee.emp_phone1 %}
                                    <a href="tel:{{ employee.emp_phone1 }}" class="text-decoration-none text-dark">
                                        <i class="fas fa-phone-alt text-primary me-1 small"></i>
                                        {{ employee.emp_phone1 }}
                                    </a>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td class="px-3">
                                    {% if employee.working_condition == 'سارى' %}
                                    <span class="badge bg-success-subtle text-success border border-success-subtle px-2 py-1">
                                        <i class="fas fa-check-circle me-1"></i>نشط
                                    </span>
                                    {% elif employee.working_condition == 'منقطع عن العمل' %}
                                    <span class="badge bg-info-subtle text-info border border-info-subtle px-2 py-1">
                                        <i class="fas fa-pause-circle me-1"></i>منقطع عن العمل
                                    </span>
                                    {% elif employee.working_condition == 'استقالة' %}
                                    <span class="badge bg-danger-subtle text-danger border border-danger-subtle px-2 py-1">
                                        <i class="fas fa-times-circle me-1"></i>استقالة
                                    </span>
                                    {% else %}
                                    <span class="badge bg-secondary-subtle text-secondary border border-secondary-subtle px-2 py-1">
                                        <i class="fas fa-question-circle me-1"></i>{{ employee.working_condition|default:"-" }}
                                    </span>
                                    {% endif %}
                                </td>
                                <td class="px-3 text-center">
                                    <div class="btn-group btn-group-sm action-buttons">
                                        <a href="{% url 'Hr:employees:detail' employee.emp_id %}" class="btn btn-outline-primary btn-sm action-btn" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if perms.Hr.change_employee or user|is_admin %}
                                            <a href="{% url 'Hr:employees:edit' employee.emp_id %}" class="btn btn-primary btn-sm action-btn">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        {% endif %}
                                        {% if perms.Hr.delete_employee or user|is_admin %}
                                            <button type="button" class="btn btn-danger btn-sm action-btn delete-employee"
                                                    data-employee-id="{{ employee.emp_id }}"
                                                    data-employee-name="{{ employee.emp_full_name }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        {% endif %}
                                        <button type="button" class="btn btn-outline-info btn-sm dropdown-toggle dropdown-toggle-split action-btn" data-bs-toggle="dropdown" aria-expanded="false">
                                            <span class="visually-hidden">المزيد</span>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li><a class="dropdown-item" href="{% url 'Hr:employees:detail' employee.emp_id %}"><i class="fas fa-id-card me-2 text-primary"></i>البطاقة الشخصية</a></li>
                                            <li><a class="dropdown-item" href="#"><i class="fas fa-print me-2 text-secondary"></i>طباعة البيانات</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-user-times me-2"></i>تغيير الحالة</a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x mb-3 text-muted"></i>
                    <p class="mb-0">لا يوجد موظفين بالمعايير المحددة.</p>
                </div>
                {% endif %}
            </div>
        </div>

        {% if employees_by_department %}
        <div class="card shadow-sm mt-4">
            <div class="card-header bg-light py-3 border-bottom">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-chart-pie me-2 text-primary"></i>
                    توزيع الموظفين حسب الأقسام
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    {% for dept in employees_by_department %}
                    <div class="col-md-6 col-lg-4">
                        <div class="border rounded p-3 h-100 d-flex flex-column">
                             <h6 class="card-title mb-1">{{ dept.dept_name }}</h6>
                             <div class="d-flex justify-content-between align-items-center mb-1">
                                 <span class="fw-bold fs-5 text-primary">{{ dept.count }}</span>
                                 {% if dept.dept_code %}
                                 <a href="{% url 'Hr:departments:detail' dept.dept_code %}" class="btn btn-sm btn-link p-0 text-decoration-none">عرض</a>
                                 {% endif %}
                             </div>
                             <div class="progress mt-auto" style="height: 6px;">
                                 <div class="progress-bar bg-primary" role="progressbar"
                                      style="width: {% widthratio dept.count total_employees 100 %}%;"
                                      aria-valuenow="{% widthratio dept.count total_employees 100 %}"
                                      aria-valuemin="0" aria-valuemax="100">
                                 </div>
                             </div>
                             <small class="text-muted mt-1">{% widthratio dept.count total_employees 100 %}% من الإجمالي</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light py-3 border-bottom">
                <h5 class="mb-0 fw-semibold">روابط سريعة</h5>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    <div class="col-xl-3 col-md-6">
                        <a href="{% url 'Hr:employees:list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-primary-subtle text-primary rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">قائمة الموظفين</h6>
                                    <p class="text-muted small mb-0">عرض وإدارة بيانات الموظفين</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-md-6">
                        <a href="{% url 'Hr:employees:create' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-success-subtle text-success rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">إضافة موظف جديد</h6>
                                    <p class="text-muted small mb-0">تسجيل بيانات موظف جديد</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-md-6">
                        <a href="{% url 'Hr:departments:list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-info-subtle text-info rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">الأقسام</h6>
                                    <p class="text-muted small mb-0">عرض وإدارة الأقسام</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-md-6">
                        <a href="{% url 'Hr:jobs:list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-warning-subtle text-warning rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">الوظائف</h6>
                                    <p class="text-muted small mb-0">عرض وإدارة المسميات الوظيفية</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إضافة حاوية للإشعارات -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-check-circle me-2"></i>
                <span class="toast-message"></span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>

<!-- إضافة مؤشر التحميل -->
<div id="loadingIndicator" class="position-fixed top-0 start-0 end-0" style="display: none;">
    <div class="progress" style="height: 3px;">
        <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary" role="progressbar" style="width: 100%"></div>
    </div>
</div>

<!-- قالب الإجراءات السريعة -->
<div class="position-fixed bottom-0 end-0 m-3">
    <div class="btn-group-vertical">
        <button id="quickActionsBtn" type="button" class="btn btn-primary rounded-circle mb-2" style="width: 50px; height: 50px;">
            <i class="fas fa-bolt"></i>
        </button>
    </div>
</div>

<!-- قائمة الإجراءات السريعة -->
<div class="quick-actions-menu position-fixed end-0 bottom-0 mb-5 me-3 bg-white rounded-3 shadow-lg p-3" style="display: none; min-width: 250px;">
    <h6 class="text-muted mb-3">إجراءات سريعة</h6>
    <div class="list-group list-group-flush">
        <a href="{% url 'Hr:employees:create' %}" class="list-group-item list-group-item-action">
            <i class="fas fa-user-plus text-primary me-2"></i>إضافة موظف جديد
        </a>
        <button class="list-group-item list-group-item-action" onclick="exportToExcel()">
            <i class="fas fa-file-excel text-success me-2"></i>تصدير إلى Excel
        </button>
        <button class="list-group-item list-group-item-action" onclick="printEmployeeList()">
            <i class="fas fa-print text-info me-2"></i>طباعة القائمة
        </button>
        {% comment %} Quick action for advanced search removed {% endcomment %}
    </div>
</div>

<!-- Keyboard Shortcuts Guide -->
<div class="modal fade" id="keyboardShortcutsModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">
                    <i class="fas fa-keyboard me-2 text-primary"></i>
                    اختصارات لوحة المفاتيح
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <kbd class="me-2">Ctrl</kbd> + <kbd class="mx-2">F</kbd>
                            <span class="text-muted">بحث سريع</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <kbd class="me-2">Ctrl</kbd> + <kbd class="mx-2">N</kbd>
                            <span class="text-muted">موظف جديد</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <kbd class="me-2">Ctrl</kbd> + <kbd class="mx-2">P</kbd>
                            <span class="text-muted">طباعة القائمة</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <kbd class="me-2">Ctrl</kbd> + <kbd class="mx-2">E</kbd>
                            <span class="text-muted">تصدير البيانات</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock content %}
{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/employee_list.css' %}">
{% endblock extra_css %}

{% block extra_js %}
<script>
    // Define Django URLs and CSRF token for use in external JS
    const employeeDetailUrlTemplate = "{% url 'Hr:employees:detail' 0 %}";
    const employeeListUrl = "{% url 'Hr:employees:list' %}";
    const newEmployeeUrl = "{% url 'Hr:employees:create' %}";
    const exportUrlTemplate = "{% url 'Hr:employees:export' %}";
    const csrfToken = "{{ csrf_token }}";
    const employeeSearchUrlTemplate = "{% url 'Hr:employees:employee_search' %}?q=QUERY_PLACEHOLDER"; // Use a placeholder for query
    const emp_id_placeholder_for_url = '0'; // Or a more unique placeholder if '0' might appear in other parts of the URL
</script>
<script src="{% static 'js/employee_list.js' %}"></script>
{% endblock extra_js %}
